#!/usr/bin/env python3
"""
3D曲面柱子自动测量系统 - 完整使用示例

本脚本演示了系统的各种使用方法，包括：
1. 基本测量流程
2. 高级校准功能
3. 批量处理
4. 结果分析和可视化
5. 参数优化
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

# 导入自定义模块
from auto_measurement import DAZ3DMeasurement
from advanced_calibration import AdvancedPlaneCalibration
from config import get_config, MEASUREMENT_CONFIG, save_config_to_file

def create_output_directories():
    """创建输出目录"""
    directories = ['results', 'results/plots', 'results/logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("输出目录已创建")

def example_basic_measurement():
    """示例1: 基本测量流程"""
    print("\n" + "="*50)
    print("示例1: 基本测量流程")
    print("="*50)
    
    # 创建测量实例
    measurer = DAZ3DMeasurement(
        min_pillar_height=1.0,
        min_pillar_area=100,
        max_noise_area=50
    )
    
    # 检查测试文件
    test_file = "N001.TXT"
    if not os.path.exists(test_file):
        print(f"警告: 测试文件 {test_file} 不存在")
        return None
    
    # 处理单个文件
    print(f"处理文件: {test_file}")
    results = measurer.process_single_file(test_file)
    
    if results:
        # 显示结果
        df_results = pd.DataFrame(results)
        print(f"\n检测到 {len(results)} 个柱子:")
        print(df_results.to_string(index=False))
        
        # 保存结果
        output_file = "results/basic_measurement_results.csv"
        measurer.save_results(df_results, output_file)
        
        # 可视化
        measurer.visualize_results(test_file, save_plot=True)
        
        return df_results
    else:
        print("未检测到柱子")
        return None

def example_advanced_calibration():
    """示例2: 高级校准功能"""
    print("\n" + "="*50)
    print("示例2: 高级校准功能")
    print("="*50)
    
    test_file = "N001.TXT"
    if not os.path.exists(test_file):
        print(f"警告: 测试文件 {test_file} 不存在")
        return
    
    # 创建基本测量器和高级校准器
    measurer = DAZ3DMeasurement()
    calibrator = AdvancedPlaneCalibration(
        initial_height_threshold=2.0,
        iteration_threshold=0.1
    )
    
    # 读取数据
    print("读取3D数据...")
    height_matrix, x_step, y_step = measurer.read_daz_file(test_file)
    print(f"数据尺寸: {height_matrix.shape}")
    print(f"步长: X={x_step:.3f}μm, Y={y_step:.3f}μm")
    
    # 执行高级校准
    print("执行高级平面校准...")
    calibrated_matrix = calibrator.adaptive_threshold_calibration(height_matrix)
    
    # 可视化校准过程
    plot_path = "results/plots/advanced_calibration_process.png"
    calibrator.visualize_calibration_process(
        height_matrix, calibrated_matrix, plot_path
    )
    
    # 比较基本校准和高级校准的效果
    basic_calibrated = measurer.calibrate_plane(height_matrix)
    
    print("\n校准效果对比:")
    print(f"原始数据标准差: {np.std(height_matrix):.3f}μm")
    print(f"基本校准后标准差: {np.std(basic_calibrated):.3f}μm")
    print(f"高级校准后标准差: {np.std(calibrated_matrix):.3f}μm")
    
    return calibrated_matrix

def example_batch_processing():
    """示例3: 批量处理"""
    print("\n" + "="*50)
    print("示例3: 批量处理")
    print("="*50)
    
    # 创建测量实例
    measurer = DAZ3DMeasurement(
        min_pillar_height=1.0,
        min_pillar_area=100,
        max_noise_area=50
    )
    
    # 批量处理当前目录的所有TXT文件
    current_dir = "."
    print(f"扫描目录: {current_dir}")
    
    df_all = measurer.batch_process(current_dir, "*.TXT")
    
    if not df_all.empty:
        print(f"\n批量处理完成，共处理 {len(df_all)} 个柱子")
        
        # 保存详细结果
        output_file = "results/batch_processing_results.csv"
        measurer.save_results(df_all, output_file)
        
        # 生成统计报告
        generate_statistics_report(df_all)
        
        return df_all
    else:
        print("未找到有效的测量结果")
        return pd.DataFrame()

def generate_statistics_report(df_results):
    """生成统计报告"""
    print("\n" + "="*30)
    print("统计报告")
    print("="*30)
    
    # 基本统计
    numeric_columns = ['height', 'area_um2', 'width_x_95%', 'width_y_95%', 'width_x_10%', 'width_y_10%']
    
    stats = df_results[numeric_columns].describe()
    print("\n基本统计信息:")
    print(stats.round(3))
    
    # 按文件分组统计
    if 'file' in df_results.columns:
        file_stats = df_results.groupby('file')[numeric_columns].agg(['count', 'mean', 'std'])
        print(f"\n按文件分组统计:")
        print(file_stats.round(3))
    
    # 保存统计报告
    stats_file = "results/statistics_report.csv"
    stats.to_csv(stats_file)
    print(f"\n统计报告已保存到: {stats_file}")
    
    # 生成统计图表
    create_statistics_plots(df_results)

def create_statistics_plots(df_results):
    """创建统计图表"""
    print("生成统计图表...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 高度分布
    axes[0, 0].hist(df_results['height'], bins=20, alpha=0.7, edgecolor='black')
    axes[0, 0].set_xlabel('高度 (μm)')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].set_title('柱子高度分布')
    
    # 面积分布
    axes[0, 1].hist(df_results['area_um2'], bins=20, alpha=0.7, edgecolor='black')
    axes[0, 1].set_xlabel('面积 (μm²)')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].set_title('柱子面积分布')
    
    # 95%高度宽度散点图
    axes[0, 2].scatter(df_results['width_x_95%'], df_results['width_y_95%'], alpha=0.7)
    axes[0, 2].set_xlabel('X方向宽度 (μm)')
    axes[0, 2].set_ylabel('Y方向宽度 (μm)')
    axes[0, 2].set_title('95%高度位置宽度分布')
    
    # 高度vs面积
    axes[1, 0].scatter(df_results['height'], df_results['area_um2'], alpha=0.7)
    axes[1, 0].set_xlabel('高度 (μm)')
    axes[1, 0].set_ylabel('面积 (μm²)')
    axes[1, 0].set_title('高度-面积关系')
    
    # 宽度比较（95% vs 10%）
    axes[1, 1].scatter(df_results['width_x_95%'], df_results['width_x_10%'], alpha=0.7, label='X方向')
    axes[1, 1].scatter(df_results['width_y_95%'], df_results['width_y_10%'], alpha=0.7, label='Y方向')
    axes[1, 1].set_xlabel('95%高度宽度 (μm)')
    axes[1, 1].set_ylabel('10%高度宽度 (μm)')
    axes[1, 1].set_title('不同高度位置宽度比较')
    axes[1, 1].legend()
    
    # 长短轴比分析
    aspect_ratio_x = df_results['width_x_95%'] / df_results['width_y_95%']
    axes[1, 2].hist(aspect_ratio_x, bins=20, alpha=0.7, edgecolor='black')
    axes[1, 2].set_xlabel('长短轴比 (X/Y)')
    axes[1, 2].set_ylabel('频次')
    axes[1, 2].set_title('柱子形状分析')
    
    plt.tight_layout()
    
    # 保存图表
    plot_file = "results/plots/statistics_plots.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"统计图表已保存到: {plot_file}")
    
    plt.show()

def example_parameter_optimization():
    """示例4: 参数优化"""
    print("\n" + "="*50)
    print("示例4: 参数优化")
    print("="*50)
    
    test_file = "N001.TXT"
    if not os.path.exists(test_file):
        print(f"警告: 测试文件 {test_file} 不存在")
        return
    
    # 测试不同参数组合
    parameter_sets = [
        {'min_pillar_height': 0.5, 'min_pillar_area': 50, 'max_noise_area': 25},
        {'min_pillar_height': 1.0, 'min_pillar_area': 100, 'max_noise_area': 50},
        {'min_pillar_height': 1.5, 'min_pillar_area': 150, 'max_noise_area': 75},
        {'min_pillar_height': 2.0, 'min_pillar_area': 200, 'max_noise_area': 100},
    ]
    
    results_comparison = []
    
    for i, params in enumerate(parameter_sets):
        print(f"\n测试参数组合 {i+1}: {params}")
        
        measurer = DAZ3DMeasurement(**params)
        results = measurer.process_single_file(test_file)
        
        result_summary = {
            'parameter_set': i+1,
            'pillar_count': len(results) if results else 0,
            'avg_height': np.mean([r['height'] for r in results]) if results else 0,
            'avg_area': np.mean([r['area_um2'] for r in results]) if results else 0,
            **params
        }
        results_comparison.append(result_summary)
    
    # 显示参数优化结果
    df_comparison = pd.DataFrame(results_comparison)
    print("\n参数优化结果:")
    print(df_comparison.to_string(index=False))
    
    # 保存参数优化结果
    comparison_file = "results/parameter_optimization.csv"
    df_comparison.to_csv(comparison_file, index=False)
    print(f"\n参数优化结果已保存到: {comparison_file}")

def example_custom_analysis():
    """示例5: 自定义分析"""
    print("\n" + "="*50)
    print("示例5: 自定义分析")
    print("="*50)
    
    test_file = "N001.TXT"
    if not os.path.exists(test_file):
        print(f"警告: 测试文件 {test_file} 不存在")
        return
    
    # 创建测量实例
    measurer = DAZ3DMeasurement()
    
    # 读取和校准数据
    height_matrix, x_step, y_step = measurer.read_daz_file(test_file)
    calibrated_matrix = measurer.calibrate_plane(height_matrix)
    
    # 自定义分析：多层高度分析
    height_levels = [0.1, 0.25, 0.5, 0.75, 0.9, 0.95]
    
    pillars = measurer.detect_pillars(calibrated_matrix, x_step, y_step)
    
    if not pillars:
        print("未检测到柱子")
        return
    
    print(f"检测到 {len(pillars)} 个柱子，进行多层高度分析...")
    
    multilevel_results = []
    
    for pillar in pillars:
        coords = pillar['coords']
        max_height = pillar['max_height']
        pillar_heights = calibrated_matrix[coords[:, 0], coords[:, 1]]
        
        pillar_analysis = {
            'pillar_id': pillar['label'],
            'max_height': max_height,
            'centroid_x': pillar['centroid'][1] * x_step,
            'centroid_y': pillar['centroid'][0] * y_step,
        }
        
        # 在每个高度层级测量宽度
        for level in height_levels:
            height_threshold = max_height * level
            mask_height = pillar_heights >= height_threshold
            
            if np.sum(mask_height) > 0:
                height_coords = coords[mask_height]
                x_coords = height_coords[:, 1]
                y_coords = height_coords[:, 0]
                
                width_x = (np.max(x_coords) - np.min(x_coords) + 1) * x_step
                width_y = (np.max(y_coords) - np.min(y_coords) + 1) * y_step
                
                pillar_analysis[f'width_x_{int(level*100)}%'] = width_x
                pillar_analysis[f'width_y_{int(level*100)}%'] = width_y
            else:
                pillar_analysis[f'width_x_{int(level*100)}%'] = 0
                pillar_analysis[f'width_y_{int(level*100)}%'] = 0
        
        multilevel_results.append(pillar_analysis)
    
    # 保存多层分析结果
    df_multilevel = pd.DataFrame(multilevel_results)
    multilevel_file = "results/multilevel_analysis.csv"
    df_multilevel.to_csv(multilevel_file, index=False)
    
    print(f"多层高度分析完成，结果已保存到: {multilevel_file}")
    print("\n多层分析结果预览:")
    print(df_multilevel.head().to_string(index=False))

def main():
    """主函数：运行所有示例"""
    print("3D曲面柱子自动测量系统 - 使用示例")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建输出目录
    create_output_directories()
    
    # 保存默认配置
    save_config_to_file("results/measurement_config.json")
    
    try:
        # 运行各个示例
        print("\n开始运行示例...")
        
        # 示例1: 基本测量
        basic_results = example_basic_measurement()
        
        # 示例2: 高级校准
        example_advanced_calibration()
        
        # 示例3: 批量处理
        batch_results = example_batch_processing()
        
        # 示例4: 参数优化
        example_parameter_optimization()
        
        # 示例5: 自定义分析
        example_custom_analysis()
        
        print("\n" + "="*60)
        print("所有示例运行完成！")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n输出文件位置:")
        print("- 测量结果: results/")
        print("- 分析图像: results/plots/")
        print("- 配置文件: results/measurement_config.json")
        
    except Exception as e:
        print(f"\n运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
