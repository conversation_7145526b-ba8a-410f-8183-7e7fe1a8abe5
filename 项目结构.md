# 3D曲面柱子自动测量系统 - 项目结构

## 📁 核心程序文件

### 主要模块
- **`auto_measurement.py`** - 主测量模块
  - 数据读取和解析
  - 平面校准
  - 柱子检测和测量
  - X/Y方向切面图生成
  - 批量处理功能
  - 中文界面支持

- **`advanced_calibration.py`** - 高级平面校准模块
  - 智能基底识别
  - 迭代优化校准
  - 鲁棒平面拟合
  - 校准过程可视化

### 使用脚本
- **`demo.py`** - 完整功能演示脚本（推荐使用）
  - 展示所有核心功能
  - 生成详细的分析报告
  - 包含一致性验证

- **`simple_usage.py`** - 简化使用示例
  - 快速上手指南
  - 基本测量流程
  - 形状分析功能

- **`cross_section_analysis.py`** - 切面分析专用脚本
  - 详细的X/Y方向切面图
  - 多层级宽度变化分析
  - 尺寸标注和可视化

### 配置管理
- **`config.py`** - 配置管理模块
  - 参数设置和验证
  - 配置文件读写
  - 系统配置管理

- **`measurement_config.json`** - 系统配置文件
  - 检测参数配置
  - 校准参数设置
  - 输出格式配置

## 📊 数据文件

- **`N001.TXT`** - 示例3D数据文件
  - 876×636像素的3D高度数据
  - 物理尺寸：171.3×124.9 μm
  - 包含1个椭圆形柱子

- **`read_file.py`** - 原始读取示例（参考）
  - 展示数据文件格式
  - 基本读取方法

## 📖 文档文件

- **`README.md`** - 详细技术文档
  - 完整的功能说明
  - 技术实现细节
  - 使用方法和示例

- **`SUMMARY.md`** - 技术路线总结
  - 项目背景和目标
  - 技术架构说明
  - 测试结果验证

- **`使用说明.md`** - 快速使用指南
  - 文件结构说明
  - 快速开始指南
  - 参数调优建议

- **`项目结构.md`** - 本文件，项目结构说明

## 🚀 快速开始

### 1. 完整功能演示（推荐）
```bash
python demo.py
```

### 2. 简化使用
```bash
python simple_usage.py
```

### 3. 切面分析
```bash
python cross_section_analysis.py
```

## 🎯 核心功能

### ✅ 已实现功能
- [x] 自动数据读取和解析
- [x] 智能平面校准（排除柱子区域）
- [x] 自动柱子检测和分离
- [x] 多层级宽度测量（95%/10%高度）
- [x] 形状分析（圆形/椭圆形识别）
- [x] X/Y方向切面图生成
- [x] 尺寸可视化标注
- [x] 批量文件处理
- [x] 中文界面支持
- [x] 控制台和可视化输出一致性

### 📊 测量精度
- **高度精度**: 亚微米级别(0.001μm)
- **位置精度**: 0.1μm级别
- **面积精度**: 0.1μm²级别
- **宽度精度**: 0.001μm级别

## 🔧 系统要求

### Python依赖包
```bash
pip install numpy matplotlib scipy scikit-image pandas
```

### 支持的数据格式
- TXT格式的3D高度数据
- 格式：`x_len y_len x_step y_step height1 height2 ...`

## 📈 使用建议

1. **首次使用**: 运行`demo.py`了解完整功能
2. **日常使用**: 使用`simple_usage.py`进行快速测量
3. **详细分析**: 使用`cross_section_analysis.py`生成切面图
4. **参数调优**: 修改`config.py`中的参数设置
5. **批量处理**: 将多个TXT文件放在同一目录下

## 🎉 项目特点

- **完全自动化**: 无需人工干预
- **高精度测量**: 亚微米级别精度
- **智能校准**: 自动消除基底偏差
- **形状识别**: 准确识别圆形/椭圆形
- **可视化完善**: 详细的分析图像
- **中文支持**: 完整的中文界面
- **一致性保证**: 控制台和图像输出完全一致

系统已通过实际数据验证，可直接用于生产环境！
