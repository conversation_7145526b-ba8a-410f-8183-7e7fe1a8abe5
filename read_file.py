import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# 读取和处理数据
with open('N001.txt', 'r') as daz_file:
    daz_data = daz_file.read().strip()

daz_list = daz_data.split(' ')
daz_header = daz_list[0:4]
x_len, y_len = map(int, daz_header[:2])
x_step, y_step = map(float, daz_header[2:])
daz_body = list(map(float, daz_list[4:]))

# 构造符合笛卡尔坐标系的矩阵
daz_matrix = np.fliplr(np.array(daz_body).reshape(y_len, x_len).T)
