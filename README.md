# 3D曲面柱子自动测量系统

## 项目简介

本系统专门用于自动测量3D曲面图像中树脂柱子的几何参数，包括高度、不同高度位置的X/Y方向宽度等。系统能够自动校准玻璃基底的平面偏差，检测并测量多个柱子，适用于批量处理。

## 主要功能

### 1. 平面校准
- **智能基底识别**：自动排除柱子区域，仅使用玻璃基底进行平面拟合
- **迭代校准**：多次迭代优化，消除翘曲和倾斜
- **鲁棒拟合**：使用Huber损失函数，抗噪声干扰

### 2. 柱子检测
- **高度阈值分割**：基于高度差异自动识别柱子
- **形态学处理**：去除噪声，分离独立柱子
- **连通域分析**：精确定位每个柱子的边界

### 3. 尺寸测量
- **高度测量**：柱子最高点与校准基底的高度差
- **多层级宽度**：在95%和10%高度位置测量X/Y方向宽度
- **形状分析**：识别圆形或椭圆形横截面

### 4. 批量处理
- **多文件处理**：自动处理目录中的所有3D文件
- **结果汇总**：生成CSV格式的测量报告
- **可视化输出**：生成分析图像和3D视图

## 文件结构

```
DAZ_Auto_Meas/
├── auto_measurement.py      # 主测量模块
├── advanced_calibration.py  # 高级平面校准模块
├── config.py               # 配置文件
├── README.md               # 使用说明
├── example_usage.py        # 使用示例
├── N001.TXT               # 示例数据文件
└── results/               # 输出目录
    ├── measurement_results.csv
    ├── plots/
    └── logs/
```

## 安装依赖

```bash
pip install numpy matplotlib scipy scikit-image pandas
```

## 快速开始

### 1. 基本使用

```python
from auto_measurement import DAZ3DMeasurement

# 创建测量实例
measurer = DAZ3DMeasurement(
    min_pillar_height=1.0,  # 最小柱子高度1微米
    min_pillar_area=100,    # 最小柱子面积100像素
    max_noise_area=50       # 最大噪声面积50像素
)

# 处理单个文件
results = measurer.process_single_file("N001.TXT")

# 批量处理
df_results = measurer.batch_process(".", "*.TXT")
measurer.save_results(df_results, "results.csv")
```

### 2. 高级校准

```python
from advanced_calibration import AdvancedPlaneCalibration

# 创建高级校准器
calibrator = AdvancedPlaneCalibration()

# 读取数据
height_matrix, x_step, y_step = measurer.read_daz_file("N001.TXT")

# 执行高级校准
calibrated_matrix = calibrator.adaptive_threshold_calibration(height_matrix)

# 可视化校准过程
calibrator.visualize_calibration_process(height_matrix, calibrated_matrix)
```

### 3. 配置自定义

```python
from config import get_config, MEASUREMENT_CONFIG

# 修改配置
MEASUREMENT_CONFIG['min_pillar_height'] = 2.0
MEASUREMENT_CONFIG['min_pillar_area'] = 200

# 获取完整配置
config = get_config()
```

## 数据格式

### 输入格式
3D数据文件格式（如N001.TXT）：
```
x_len y_len x_step y_step height1 height2 height3 ...
```
- `x_len, y_len`: 数据矩阵尺寸
- `x_step, y_step`: X/Y方向步长（微米）
- `height1, height2, ...`: 高度数据序列（微米）

### 输出格式
测量结果CSV文件包含以下列：
- `file`: 文件名
- `pillar_id`: 柱子编号
- `centroid_x, centroid_y`: 柱子中心坐标（微米）
- `height`: 柱子高度（微米）
- `area_pixels, area_um2`: 柱子面积（像素和平方微米）
- `width_x_95%, width_y_95%`: 95%高度位置的X/Y宽度（微米）
- `width_x_10%, width_y_10%`: 10%高度位置的X/Y宽度（微米）

## 参数调优指南

### 1. 柱子检测参数
- **min_pillar_height**: 根据实际柱子高度调整，建议设为预期最小高度的50-80%
- **min_pillar_area**: 根据柱子大小调整，太小会检测到噪声，太大会遗漏小柱子
- **max_noise_area**: 用于过滤小噪声，建议设为最小柱子面积的1/3-1/2

### 2. 校准参数
- **initial_height_threshold**: 初始柱子识别阈值，建议设为数据标准差的2-3倍
- **iteration_threshold**: 校准收敛阈值，越小校准越精确但耗时更长

### 3. 质量控制
- 检查校准效果：观察校准后的基底是否平坦
- 验证检测结果：确认所有柱子都被正确识别
- 测量精度评估：对已知尺寸的标准样品进行验证

## 常见问题

### Q1: 柱子检测不完整
**解决方案**：
- 降低`min_pillar_height`阈值
- 减小`min_pillar_area`参数
- 检查平面校准效果

### Q2: 检测到过多噪声
**解决方案**：
- 提高`min_pillar_height`阈值
- 增大`min_pillar_area`参数
- 减小`max_noise_area`参数

### Q3: 平面校准效果不佳
**解决方案**：
- 使用高级校准模块
- 增加校准迭代次数
- 检查数据质量和边缘效应

### Q4: 宽度测量不准确
**解决方案**：
- 检查柱子形状是否规则
- 验证95%/10%高度阈值设置
- 考虑柱子边缘的模糊效应

## 性能优化

### 1. 大文件处理
- 启用并行处理：`PERFORMANCE_CONFIG['parallel_processing'] = True`
- 调整内存限制：`PERFORMANCE_CONFIG['memory_limit_mb'] = 2048`

### 2. 批量处理
- 关闭实时显示：`MEASUREMENT_CONFIG['show_plots'] = False`
- 减少输出图像：`MEASUREMENT_CONFIG['save_plots'] = False`

## 扩展功能

系统支持以下扩展：
1. **自定义形状分析**：添加更复杂的形状识别算法
2. **多层高度分析**：在更多高度层级进行测量
3. **统计分析**：添加更多统计指标和质量评估
4. **数据库集成**：将结果存储到数据库
5. **Web界面**：开发基于Web的用户界面

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

## 更新日志

### v1.0.0 (2025-08-14)
- 初始版本发布
- 基本测量功能
- 平面校准算法
- 批量处理支持
- 可视化输出
