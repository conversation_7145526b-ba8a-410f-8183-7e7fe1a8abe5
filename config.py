"""
3D曲面柱子自动测量系统配置文件
"""

# 测量参数配置
MEASUREMENT_CONFIG = {
    # 柱子检测参数
    'min_pillar_height': 1.0,      # 柱子最小高度阈值(微米)
    'min_pillar_area': 100,        # 柱子最小面积阈值(像素)
    'max_noise_area': 50,          # 噪声最大面积阈值(像素)
    
    # 平面校准参数
    'initial_height_threshold': 2.0,  # 初始高度阈值(微米)
    'iteration_threshold': 0.5,       # 迭代收敛阈值
    'max_calibration_iterations': 3,  # 最大校准迭代次数
    
    # 文件处理参数
    'input_pattern': '*.TXT',         # 输入文件匹配模式
    'output_format': 'csv',           # 输出格式: 'csv' 或 'excel'
    
    # 可视化参数
    'save_plots': True,               # 是否保存分析图像
    'plot_dpi': 300,                  # 图像分辨率
    'show_plots': False,              # 是否显示图像（批量处理时建议False）
}

# 高级检测参数
ADVANCED_CONFIG = {
    # 柱子形状分析
    'analyze_shape': True,            # 是否分析柱子形状（圆形/椭圆形）
    'circularity_threshold': 0.8,     # 圆形度阈值
    'aspect_ratio_threshold': 1.5,    # 长短轴比阈值
    
    # 高度分析层级
    'height_levels': [0.1, 0.25, 0.5, 0.75, 0.95],  # 分析的高度比例
    
    # 质量控制
    'enable_quality_check': True,     # 启用质量检查
    'max_height_variation': 0.2,      # 最大高度变异系数
    'min_width_ratio': 0.1,           # 最小宽度比（宽度/高度）
    'max_width_ratio': 10.0,          # 最大宽度比
}

# 输出配置
OUTPUT_CONFIG = {
    # 基本测量结果列
    'basic_columns': [
        'file', 'pillar_id', 'centroid_x', 'centroid_y',
        'height', 'area_pixels', 'area_um2',
        'width_x_95%', 'width_y_95%',
        'width_x_10%', 'width_y_10%'
    ],
    
    # 扩展分析结果列
    'extended_columns': [
        'shape_type', 'circularity', 'aspect_ratio',
        'height_uniformity', 'volume_estimate',
        'quality_score'
    ],
    
    # 统计汇总
    'include_statistics': True,       # 包含统计汇总
    'statistics_columns': [
        'count', 'mean', 'std', 'min', 'max', 'median'
    ]
}

# 文件路径配置
PATH_CONFIG = {
    'input_dir': '.',                 # 输入目录
    'output_dir': './results',        # 输出目录
    'plot_dir': './plots',            # 图像输出目录
    'log_dir': './logs',              # 日志目录
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',                  # 日志级别: DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file_enabled': True,             # 是否保存日志文件
    'console_enabled': True,          # 是否显示控制台日志
}

# 性能配置
PERFORMANCE_CONFIG = {
    'parallel_processing': False,     # 是否启用并行处理
    'max_workers': 4,                 # 最大工作进程数
    'memory_limit_mb': 1024,          # 内存限制(MB)
    'chunk_size': 10,                 # 批处理块大小
}

# 验证配置有效性
def validate_config():
    """验证配置参数的有效性"""
    errors = []
    
    # 检查测量参数
    if MEASUREMENT_CONFIG['min_pillar_height'] <= 0:
        errors.append("min_pillar_height 必须大于0")
    
    if MEASUREMENT_CONFIG['min_pillar_area'] <= 0:
        errors.append("min_pillar_area 必须大于0")
    
    # 检查高度层级
    height_levels = ADVANCED_CONFIG['height_levels']
    if not all(0 <= level <= 1 for level in height_levels):
        errors.append("height_levels 中的值必须在0-1之间")
    
    # 检查阈值
    if not 0 <= ADVANCED_CONFIG['circularity_threshold'] <= 1:
        errors.append("circularity_threshold 必须在0-1之间")
    
    if errors:
        raise ValueError("配置错误:\n" + "\n".join(errors))
    
    return True

# 获取完整配置
def get_config():
    """获取完整的配置字典"""
    validate_config()
    
    return {
        'measurement': MEASUREMENT_CONFIG,
        'advanced': ADVANCED_CONFIG,
        'output': OUTPUT_CONFIG,
        'paths': PATH_CONFIG,
        'logging': LOGGING_CONFIG,
        'performance': PERFORMANCE_CONFIG
    }

# 保存配置到文件
def save_config_to_file(filepath='measurement_config.json'):
    """保存配置到JSON文件"""
    import json
    
    config = get_config()
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)
    
    print(f"配置已保存到: {filepath}")

# 从文件加载配置
def load_config_from_file(filepath='measurement_config.json'):
    """从JSON文件加载配置"""
    import json
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新全局配置
        MEASUREMENT_CONFIG.update(config.get('measurement', {}))
        ADVANCED_CONFIG.update(config.get('advanced', {}))
        OUTPUT_CONFIG.update(config.get('output', {}))
        PATH_CONFIG.update(config.get('paths', {}))
        LOGGING_CONFIG.update(config.get('logging', {}))
        PERFORMANCE_CONFIG.update(config.get('performance', {}))
        
        print(f"配置已从 {filepath} 加载")
        return True
        
    except FileNotFoundError:
        print(f"配置文件 {filepath} 不存在，使用默认配置")
        return False
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}")
        return False

if __name__ == "__main__":
    # 验证并保存默认配置
    try:
        validate_config()
        print("配置验证通过")
        
        # 保存默认配置文件
        save_config_to_file()
        
        # 显示当前配置
        config = get_config()
        print("\n当前配置:")
        for section, params in config.items():
            print(f"\n[{section.upper()}]")
            for key, value in params.items():
                print(f"  {key}: {value}")
                
    except ValueError as e:
        print(f"配置错误: {e}")
