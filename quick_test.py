#!/usr/bin/env python3
"""
快速测试脚本 - 验证数据读取和基本功能
"""

import numpy as np
import os

def read_daz_file(filepath):
    """按照read_file.py的方式读取DAZ文件"""
    print(f"读取文件: {filepath}")
    
    # 检查文件是否存在
    if not os.path.exists(filepath):
        print(f"错误: 文件 {filepath} 不存在")
        return None, None, None
    
    try:
        # 按照read_file.py的方式读取数据
        with open(filepath, 'r') as daz_file:
            daz_data = daz_file.read().strip()
        
        daz_list = daz_data.split(' ')
        daz_header = daz_list[0:4]
        x_len, y_len = map(int, daz_header[:2])
        x_step, y_step = map(float, daz_header[2:])
        daz_body = list(map(float, daz_list[4:]))
        
        print(f"文件头信息:")
        print(f"  X长度: {x_len}")
        print(f"  Y长度: {y_len}")
        print(f"  X步长: {x_step} μm")
        print(f"  Y步长: {y_step} μm")
        print(f"  数据点数: {len(daz_body)}")
        print(f"  预期数据点数: {x_len * y_len}")
        
        if len(daz_body) != x_len * y_len:
            print(f"警告: 数据点数不匹配!")
            return None, None, None
        
        # 构造符合笛卡尔坐标系的矩阵 - 与read_file.py完全一致
        daz_matrix = np.fliplr(np.array(daz_body).reshape(y_len, x_len).T)
        
        print(f"矩阵构造成功:")
        print(f"  矩阵形状: {daz_matrix.shape}")
        print(f"  高度范围: {np.min(daz_matrix):.3f} - {np.max(daz_matrix):.3f} μm")
        print(f"  平均高度: {np.mean(daz_matrix):.3f} μm")
        print(f"  标准差: {np.std(daz_matrix):.3f} μm")
        
        return daz_matrix, x_step, y_step
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None, None, None

def simple_plane_calibration(height_matrix):
    """简单的平面校准"""
    print(f"\n执行平面校准...")
    
    try:
        rows, cols = height_matrix.shape
        x, y = np.meshgrid(np.arange(cols), np.arange(rows))
        
        # 使用最小二乘法拟合平面: z = ax + by + c
        A = np.column_stack([x.flatten(), y.flatten(), np.ones(x.size)])
        b = height_matrix.flatten()
        
        # 求解平面参数
        plane_params, residuals, rank, s = np.linalg.lstsq(A, b, rcond=None)
        a, b_param, c = plane_params
        
        print(f"平面参数: a={a:.6f}, b={b_param:.6f}, c={c:.3f}")
        
        # 计算拟合平面
        fitted_plane = a * x + b_param * y + c
        
        # 校准后的数据
        calibrated_matrix = height_matrix - fitted_plane
        
        print(f"校准结果:")
        print(f"  校准前标准差: {np.std(height_matrix):.3f} μm")
        print(f"  校准后标准差: {np.std(calibrated_matrix):.3f} μm")
        print(f"  校准后平均值: {np.mean(calibrated_matrix):.6f} μm")
        
        return calibrated_matrix
        
    except Exception as e:
        print(f"平面校准时出错: {e}")
        return None

def simple_pillar_detection(calibrated_matrix, x_step, y_step, threshold=1.0):
    """简单的柱子检测"""
    print(f"\n执行柱子检测 (阈值: {threshold} μm)...")
    
    try:
        # 二值化
        binary_mask = calibrated_matrix > threshold
        high_points = np.sum(binary_mask)
        total_points = binary_mask.size
        
        print(f"高于阈值的点数: {high_points} / {total_points} ({high_points/total_points*100:.1f}%)")
        
        if high_points == 0:
            print("未检测到高于阈值的区域")
            return []
        
        # 使用scipy进行连通域分析
        try:
            from scipy import ndimage
            labeled_array, num_features = ndimage.label(binary_mask)
            print(f"检测到 {num_features} 个连通区域")
            
            # 分析每个区域
            pillars = []
            for i in range(1, num_features + 1):
                region_mask = labeled_array == i
                region_area = np.sum(region_mask)
                
                # 过滤太小的区域
                if region_area < 50:  # 最小50像素
                    continue
                
                # 计算区域属性
                coords = np.where(region_mask)
                centroid_y = np.mean(coords[0])
                centroid_x = np.mean(coords[1])
                max_height = np.max(calibrated_matrix[region_mask])
                mean_height = np.mean(calibrated_matrix[region_mask])
                
                pillar_info = {
                    'id': i,
                    'area_pixels': region_area,
                    'area_um2': region_area * x_step * y_step,
                    'centroid_x_um': centroid_x * x_step,
                    'centroid_y_um': centroid_y * y_step,
                    'max_height': max_height,
                    'mean_height': mean_height
                }
                pillars.append(pillar_info)
            
            print(f"有效柱子数量: {len(pillars)}")
            
            if pillars:
                print("柱子详细信息:")
                for pillar in pillars:
                    print(f"  柱子{pillar['id']}: "
                          f"高度={pillar['max_height']:.2f}μm, "
                          f"面积={pillar['area_um2']:.1f}μm², "
                          f"中心=({pillar['centroid_x_um']:.1f}, {pillar['centroid_y_um']:.1f})μm")
            
            return pillars
            
        except ImportError:
            print("scipy未安装，使用简化的检测方法")
            # 简化的检测：只统计高点
            coords = np.where(binary_mask)
            if len(coords[0]) > 0:
                centroid_y = np.mean(coords[0])
                centroid_x = np.mean(coords[1])
                max_height = np.max(calibrated_matrix[binary_mask])
                
                pillar_info = {
                    'id': 1,
                    'area_pixels': high_points,
                    'area_um2': high_points * x_step * y_step,
                    'centroid_x_um': centroid_x * x_step,
                    'centroid_y_um': centroid_y * y_step,
                    'max_height': max_height,
                    'mean_height': np.mean(calibrated_matrix[binary_mask])
                }
                
                print(f"检测到1个柱子区域:")
                print(f"  高度={pillar_info['max_height']:.2f}μm, "
                      f"面积={pillar_info['area_um2']:.1f}μm², "
                      f"中心=({pillar_info['centroid_x_um']:.1f}, {pillar_info['centroid_y_um']:.1f})μm")
                
                return [pillar_info]
            else:
                return []
        
    except Exception as e:
        print(f"柱子检测时出错: {e}")
        return []

def main():
    """主测试函数"""
    print("3D曲面柱子测量系统 - 快速测试")
    print("=" * 50)
    
    # 查找测试文件
    test_files = []
    for filename in ['N001.TXT', 'N001.txt', 'n001.txt']:
        if os.path.exists(filename):
            test_files.append(filename)
    
    if not test_files:
        print("未找到测试文件 (N001.TXT, N001.txt, n001.txt)")
        print("请确保数据文件在当前目录中")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    # 步骤1: 读取数据
    height_matrix, x_step, y_step = read_daz_file(test_file)
    
    if height_matrix is None:
        print("数据读取失败，测试终止")
        return
    
    # 步骤2: 平面校准
    calibrated_matrix = simple_plane_calibration(height_matrix)
    
    if calibrated_matrix is None:
        print("平面校准失败，测试终止")
        return
    
    # 步骤3: 柱子检测
    pillars = simple_pillar_detection(calibrated_matrix, x_step, y_step)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"✓ 数据读取成功: {height_matrix.shape}")
    print(f"✓ 平面校准成功: 标准差从 {np.std(height_matrix):.3f} 降至 {np.std(calibrated_matrix):.3f} μm")
    print(f"✓ 柱子检测成功: 发现 {len(pillars)} 个柱子")
    
    if pillars:
        avg_height = np.mean([p['max_height'] for p in pillars])
        avg_area = np.mean([p['area_um2'] for p in pillars])
        print(f"  平均柱子高度: {avg_height:.2f} μm")
        print(f"  平均柱子面积: {avg_area:.1f} μm²")
    
    print("\n基本功能验证完成！可以运行完整的测量程序。")

if __name__ == "__main__":
    main()
