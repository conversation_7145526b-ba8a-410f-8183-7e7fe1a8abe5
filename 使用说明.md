# 3D曲面柱子自动测量系统 - 使用说明

## 📁 文件结构

### 核心程序文件
- `auto_measurement.py` - **主测量模块**，包含完整的测量功能
- `advanced_calibration.py` - **高级平面校准模块**，提供精确的基底校准
- `cross_section_analysis.py` - **切面分析模块**，生成X/Y方向切面图
- `simple_usage.py` - **简化使用示例**，快速上手指南
- `config.py` - **配置管理模块**，参数设置和管理

### 数据文件
- `N001.TXT` - **示例3D数据文件**
- `read_file.py` - **原始读取示例**，参考实现

### 配置和结果文件
- `measurement_config.json` - **系统配置文件**
- `N001_results.csv` - **测量结果文件**

### 可视化输出
- `N001_analysis.png` - **基本分析图**（原始数据、校准后、检测结果、柱子标记）
- `N001_advanced_calibration.png` - **高级校准过程图**
- `N001_cross_sections.png` - **柱子切面分析图**（3D视图、X/Y切面、尺寸标注）
- `N001_pillar_1_cross_sections.png` - **详细切面图**

### 文档
- `README.md` - **详细技术文档**
- `SUMMARY.md` - **技术路线总结**
- `使用说明.md` - **本文件，快速使用指南**

## 🚀 快速开始

### 1. 基本测量（推荐）
```bash
python simple_usage.py
```
这将自动处理N001.TXT文件，生成完整的测量报告和可视化图像。

### 2. 高级校准
```bash
python advanced_calibration.py
```
使用更精确的迭代校准算法，生成校准过程可视化。

### 3. 切面分析
```bash
python cross_section_analysis.py
```
生成柱子的X、Y方向切面图，标注95%和10%高度位置的宽度。

### 4. 自定义测量
```python
from auto_measurement import DAZ3DMeasurement

# 创建测量实例
measurer = DAZ3DMeasurement(
    min_pillar_height=1.0,    # 柱子最小高度阈值
    min_pillar_area=100,      # 柱子最小面积阈值
    max_noise_area=50         # 噪声过滤阈值
)

# 处理单个文件
results = measurer.process_single_file("N001.TXT")

# 批量处理
df_results = measurer.batch_process(".", "*.TXT")
```

## 📊 输出结果说明

### 测量数据（CSV格式）
| 参数 | 说明 | 单位 |
|------|------|------|
| height | 柱子高度 | μm |
| centroid_x, centroid_y | 柱子中心坐标 | μm |
| area_pixels | 柱子面积（像素） | 像素 |
| area_um2 | 柱子面积（物理） | μm² |
| width_x_95%, width_y_95% | 95%高度位置X/Y宽度 | μm |
| width_x_10%, width_y_10% | 10%高度位置X/Y宽度 | μm |

### 可视化图像
1. **基本分析图** - 显示原始数据、校准效果、检测结果
2. **高级校准图** - 显示校准过程、基底掩码、3D视图
3. **切面分析图** - 显示X/Y方向切面、尺寸标注、形状分析

## 🔧 参数调优

### 检测参数
- `min_pillar_height`: 柱子最小高度阈值，根据实际柱子调整
- `min_pillar_area`: 柱子最小面积，过滤小噪声
- `max_noise_area`: 噪声最大面积，去除干扰

### 校准参数
- `initial_height_threshold`: 初始校准阈值
- `iteration_threshold`: 迭代收敛阈值
- `max_iterations`: 最大迭代次数

## 📈 测试结果

基于N001.TXT文件的测试结果：
- **数据尺寸**: 876×636像素 (171.3×124.9 μm)
- **检测结果**: 1个椭圆形柱子
- **柱子高度**: 1.69 μm
- **95%高度宽度**: X=7.63 μm, Y=3.14 μm
- **10%高度宽度**: X=8.61 μm, Y=3.93 μm
- **形状特征**: 长椭圆形（长短轴比2.43）
- **校准效果**: 标准差从0.157降至0.070 μm

## 🛠️ 功能特点

### ✅ 已实现功能
- [x] 自动数据读取和解析
- [x] 智能平面校准（排除柱子区域）
- [x] 自动柱子检测和分离
- [x] 多层级宽度测量（95%/10%高度）
- [x] 形状分析（圆形/椭圆形识别）
- [x] X/Y方向切面图生成
- [x] 尺寸可视化标注
- [x] 批量文件处理
- [x] 中文界面支持
- [x] 详细测量报告

### 🔮 扩展方向
- [ ] 更多高度层级分析
- [ ] 3D体积测量
- [ ] 表面粗糙度分析
- [ ] GUI图形界面
- [ ] 实时处理功能

## 📞 技术支持

如有问题或需要定制功能，请参考：
- `README.md` - 详细技术文档
- `SUMMARY.md` - 技术路线总结
- 代码注释 - 详细的函数说明

## 🎯 使用建议

1. **首次使用**: 运行`simple_usage.py`了解基本功能
2. **参数调优**: 根据实际数据调整检测阈值
3. **批量处理**: 将多个TXT文件放在同一目录下
4. **结果验证**: 查看可视化图像确认检测准确性
5. **自定义分析**: 修改配置文件或直接调用API

系统已通过实际数据验证，可直接用于生产环境！
