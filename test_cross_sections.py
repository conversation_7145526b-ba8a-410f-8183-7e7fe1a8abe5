#!/usr/bin/env python3
"""
测试切面分析功能和中文显示
"""

import numpy as np
import matplotlib.pyplot as plt
from auto_measurement import DAZ3DMeasurement
import os

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_chinese_display():
    """测试中文显示功能"""
    print("测试中文显示功能...")
    
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # 创建测试数据
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    ax.plot(x, y, 'b-', linewidth=2, label='正弦波')
    ax.set_xlabel('X轴 (微米)')
    ax.set_ylabel('Y轴 (微米)')
    ax.set_title('中文显示测试图')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加中文注释
    ax.text(5, 0.5, '这是中文注释', fontsize=14, ha='center', 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('chinese_test.png', dpi=150, bbox_inches='tight')
    print("中文显示测试图已保存到: chinese_test.png")
    plt.show()

def test_single_pillar_analysis():
    """测试单个柱子的详细分析"""
    print("\n测试单个柱子的详细分析...")
    
    test_file = "N001.TXT"
    if not os.path.exists(test_file):
        print(f"测试文件 {test_file} 不存在")
        return
    
    # 创建测量实例
    measurer = DAZ3DMeasurement(
        min_pillar_height=1.0,
        min_pillar_area=100,
        max_noise_area=50
    )
    
    # 读取和处理数据
    height_matrix, x_step, y_step = measurer.read_daz_file(test_file)
    calibrated_matrix = measurer.calibrate_plane(height_matrix)
    pillars = measurer.detect_pillars(calibrated_matrix, x_step, y_step)
    
    if not pillars:
        print("未检测到柱子")
        return
    
    print(f"检测到 {len(pillars)} 个柱子")
    
    # 分析第一个柱子
    pillar = pillars[0]
    
    # 生成切面分析图
    print("生成切面分析图...")
    measurer.visualize_pillar_cross_sections(pillar, calibrated_matrix, test_file)
    
    # 输出详细信息
    print(f"\n柱子详细信息:")
    print(f"  中心位置: ({pillar['centroid'][1]*x_step:.2f}, {pillar['centroid'][0]*y_step:.2f}) μm")
    print(f"  最大高度: {pillar['max_height']:.3f} μm")
    print(f"  面积: {pillar['area']} 像素 ({pillar['area']*x_step*y_step:.2f} μm²)")
    
    # 计算95%和10%高度的宽度
    coords = pillar['coords']
    pillar_heights = calibrated_matrix[coords[:, 0], coords[:, 1]]
    
    for level, name in [(0.95, '95%'), (0.10, '10%')]:
        height_threshold = pillar['max_height'] * level
        mask_height = pillar_heights >= height_threshold
        
        if np.sum(mask_height) > 0:
            height_coords = coords[mask_height]
            x_coords = height_coords[:, 1]
            y_coords = height_coords[:, 0]
            
            width_x = (np.max(x_coords) - np.min(x_coords) + 1) * x_step
            width_y = (np.max(y_coords) - np.min(y_coords) + 1) * y_step
            
            print(f"  {name}高度位置宽度: X={width_x:.2f} μm, Y={width_y:.2f} μm")

def create_detailed_cross_section_plot():
    """创建详细的切面分析图"""
    print("\n创建详细的切面分析图...")
    
    test_file = "N001.TXT"
    if not os.path.exists(test_file):
        print(f"测试文件 {test_file} 不存在")
        return
    
    # 读取数据
    measurer = DAZ3DMeasurement()
    height_matrix, x_step, y_step = measurer.read_daz_file(test_file)
    calibrated_matrix = measurer.calibrate_plane(height_matrix)
    pillars = measurer.detect_pillars(calibrated_matrix, x_step, y_step)
    
    if not pillars:
        print("未检测到柱子")
        return
    
    pillar = pillars[0]
    coords = pillar['coords']
    max_height = pillar['max_height']
    centroid = pillar['centroid']
    
    # 获取柱子区域
    min_row, min_col, max_row, max_col = pillar['bbox']
    padding = 20
    min_row = max(0, min_row - padding)
    min_col = max(0, min_col - padding)
    max_row = min(calibrated_matrix.shape[0], max_row + padding)
    max_col = min(calibrated_matrix.shape[1], max_col + padding)
    
    pillar_region = calibrated_matrix[min_row:max_row, min_col:max_col]
    center_row = int(centroid[0] - min_row)
    center_col = int(centroid[1] - min_col)
    
    # 创建图形
    fig = plt.figure(figsize=(16, 12))
    
    # 1. 3D视图
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    y_coords, x_coords = np.meshgrid(
        np.arange(pillar_region.shape[1]) * x_step,
        np.arange(pillar_region.shape[0]) * y_step
    )
    
    mask = pillar_region > 0.1
    if np.any(mask):
        surf = ax1.plot_surface(x_coords, y_coords, pillar_region, 
                               cmap='viridis', alpha=0.8)
    
    ax1.set_xlabel('X (μm)')
    ax1.set_ylabel('Y (μm)')
    ax1.set_zlabel('高度 (μm)')
    ax1.set_title('柱子3D视图')
    
    # 2. 俯视图
    ax2 = fig.add_subplot(2, 3, 2)
    im = ax2.imshow(pillar_region, cmap='viridis', extent=[
        0, pillar_region.shape[1] * x_step,
        pillar_region.shape[0] * y_step, 0
    ])
    
    # 标注切面线和中心
    ax2.axhline(y=center_row * y_step, color='red', linestyle='-', linewidth=2, alpha=0.8, label='X方向切面')
    ax2.axvline(x=center_col * x_step, color='blue', linestyle='-', linewidth=2, alpha=0.8, label='Y方向切面')
    ax2.plot(center_col * x_step, center_row * y_step, 'wo', markersize=8, 
             markeredgecolor='black', markeredgewidth=2, label='柱子中心')
    
    ax2.set_xlabel('X (μm)')
    ax2.set_ylabel('Y (μm)')
    ax2.set_title('柱子俯视图及切面位置')
    ax2.legend()
    plt.colorbar(im, ax=ax2, label='高度 (μm)')
    
    # 3. X方向切面
    ax3 = fig.add_subplot(2, 3, 4)
    if 0 <= center_row < pillar_region.shape[0]:
        x_profile = pillar_region[center_row, :]
        x_coords_1d = np.arange(len(x_profile)) * x_step
        
        ax3.plot(x_coords_1d, x_profile, 'b-', linewidth=3, label='高度轮廓')
        
        # 标注95%和10%高度线
        height_95 = max_height * 0.95
        height_10 = max_height * 0.10
        
        ax3.axhline(y=height_95, color='r', linestyle='--', linewidth=2, alpha=0.8, label=f'95%高度 ({height_95:.2f}μm)')
        ax3.axhline(y=height_10, color='g', linestyle='--', linewidth=2, alpha=0.8, label=f'10%高度 ({height_10:.2f}μm)')
        
        # 计算并标注宽度
        above_95 = x_profile >= height_95
        if np.any(above_95):
            indices_95 = np.where(above_95)[0]
            if len(indices_95) > 0:
                width_95_start = indices_95[0] * x_step
                width_95_end = indices_95[-1] * x_step
                width_95 = width_95_end - width_95_start
                
                ax3.annotate('', xy=(width_95_start, height_95), xytext=(width_95_end, height_95),
                           arrowprops=dict(arrowstyle='<->', color='red', lw=2))
                ax3.text((width_95_start + width_95_end)/2, height_95 + max_height*0.05, 
                        f'95%宽度: {width_95:.2f}μm', ha='center', color='red', fontweight='bold')
        
        above_10 = x_profile >= height_10
        if np.any(above_10):
            indices_10 = np.where(above_10)[0]
            if len(indices_10) > 0:
                width_10_start = indices_10[0] * x_step
                width_10_end = indices_10[-1] * x_step
                width_10 = width_10_end - width_10_start
                
                ax3.annotate('', xy=(width_10_start, height_10), xytext=(width_10_end, height_10),
                           arrowprops=dict(arrowstyle='<->', color='green', lw=2))
                ax3.text((width_10_start + width_10_end)/2, height_10 - max_height*0.1, 
                        f'10%宽度: {width_10:.2f}μm', ha='center', color='green', fontweight='bold')
    
    ax3.set_xlabel('X方向位置 (μm)')
    ax3.set_ylabel('高度 (μm)')
    ax3.set_title('X方向切面图')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. Y方向切面
    ax4 = fig.add_subplot(2, 3, 5)
    if 0 <= center_col < pillar_region.shape[1]:
        y_profile = pillar_region[:, center_col]
        y_coords_1d = np.arange(len(y_profile)) * y_step
        
        ax4.plot(y_coords_1d, y_profile, 'b-', linewidth=3, label='高度轮廓')
        
        ax4.axhline(y=height_95, color='r', linestyle='--', linewidth=2, alpha=0.8, label=f'95%高度 ({height_95:.2f}μm)')
        ax4.axhline(y=height_10, color='g', linestyle='--', linewidth=2, alpha=0.8, label=f'10%高度 ({height_10:.2f}μm)')
        
        # 计算并标注宽度
        above_95 = y_profile >= height_95
        if np.any(above_95):
            indices_95 = np.where(above_95)[0]
            if len(indices_95) > 0:
                width_95_start = indices_95[0] * y_step
                width_95_end = indices_95[-1] * y_step
                width_95 = width_95_end - width_95_start
                
                ax4.annotate('', xy=(width_95_start, height_95), xytext=(width_95_end, height_95),
                           arrowprops=dict(arrowstyle='<->', color='red', lw=2))
                ax4.text((width_95_start + width_95_end)/2, height_95 + max_height*0.05, 
                        f'95%宽度: {width_95:.2f}μm', ha='center', color='red', fontweight='bold')
        
        above_10 = y_profile >= height_10
        if np.any(above_10):
            indices_10 = np.where(above_10)[0]
            if len(indices_10) > 0:
                width_10_start = indices_10[0] * y_step
                width_10_end = indices_10[-1] * y_step
                width_10 = width_10_end - width_10_start
                
                ax4.annotate('', xy=(width_10_start, height_10), xytext=(width_10_end, height_10),
                           arrowprops=dict(arrowstyle='<->', color='green', lw=2))
                ax4.text((width_10_start + width_10_end)/2, height_10 - max_height*0.1, 
                        f'10%宽度: {width_10:.2f}μm', ha='center', color='green', fontweight='bold')
    
    ax4.set_xlabel('Y方向位置 (μm)')
    ax4.set_ylabel('高度 (μm)')
    ax4.set_title('Y方向切面图')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 尺寸汇总表
    ax5 = fig.add_subplot(2, 3, (3, 6))
    ax5.axis('off')
    
    # 创建尺寸汇总文本
    summary_text = f"""
柱子尺寸测量汇总

基本信息:
• 柱子中心: ({centroid[1]*x_step:.2f}, {centroid[0]*y_step:.2f}) μm
• 最大高度: {max_height:.3f} μm
• 柱子面积: {pillar['area']*x_step*y_step:.2f} μm²

95%高度位置测量:
• X方向宽度: {width_x_95:.2f} μm
• Y方向宽度: {width_y_95:.2f} μm
• 长短轴比: {max(width_x_95, width_y_95)/min(width_x_95, width_y_95):.2f}

10%高度位置测量:
• X方向宽度: {width_x_10:.2f} μm
• Y方向宽度: {width_y_10:.2f} μm
• 长短轴比: {max(width_x_10, width_y_10)/min(width_x_10, width_y_10):.2f}

形状特征:
• 95%高度形状: {"长椭圆形" if max(width_x_95, width_y_95)/min(width_x_95, width_y_95) > 2 else "椭圆形" if max(width_x_95, width_y_95)/min(width_x_95, width_y_95) > 1.2 else "近似圆形"}
• 10%高度形状: {"长椭圆形" if max(width_x_10, width_y_10)/min(width_x_10, width_y_10) > 2 else "椭圆形" if max(width_x_10, width_y_10)/min(width_x_10, width_y_10) > 1.2 else "近似圆形"}
"""
    
    ax5.text(0.05, 0.95, summary_text, transform=ax5.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图像
    plot_path = "detailed_cross_section_analysis.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"详细切面分析图已保存到: {plot_path}")
    
    plt.show()

def main():
    """主函数"""
    print("切面分析功能测试")
    print("=" * 40)
    
    # 1. 测试中文显示
    test_chinese_display()
    
    # 2. 测试单个柱子分析
    test_single_pillar_analysis()
    
    # 3. 创建详细的切面分析图
    create_detailed_cross_section_plot()
    
    print("\n所有测试完成！")
    print("生成的文件:")
    print("- chinese_test.png: 中文显示测试")
    print("- N001_cross_sections.png: 柱子切面分析")
    print("- detailed_cross_section_analysis.png: 详细切面分析")

if __name__ == "__main__":
    main()
