{"measurement": {"min_pillar_height": 1.0, "min_pillar_area": 100, "max_noise_area": 50, "initial_height_threshold": 2.0, "iteration_threshold": 0.5, "max_calibration_iterations": 3, "input_pattern": "*.TXT", "output_format": "csv", "save_plots": true, "plot_dpi": 300, "show_plots": false}, "advanced": {"analyze_shape": true, "circularity_threshold": 0.8, "aspect_ratio_threshold": 1.5, "height_levels": [0.1, 0.25, 0.5, 0.75, 0.95], "enable_quality_check": true, "max_height_variation": 0.2, "min_width_ratio": 0.1, "max_width_ratio": 10.0}, "output": {"basic_columns": ["file", "pillar_id", "centroid_x", "centroid_y", "height", "area_pixels", "area_um2", "width_x_95%", "width_y_95%", "width_x_10%", "width_y_10%"], "extended_columns": ["shape_type", "circularity", "aspect_ratio", "height_uniformity", "volume_estimate", "quality_score"], "include_statistics": true, "statistics_columns": ["count", "mean", "std", "min", "max", "median"]}, "paths": {"input_dir": ".", "output_dir": "./results", "plot_dir": "./plots", "log_dir": "./logs"}, "logging": {"level": "INFO", "format": "%(asctime)s - %(levelname)s - %(message)s", "file_enabled": true, "console_enabled": true}, "performance": {"parallel_processing": false, "max_workers": 4, "memory_limit_mb": 1024, "chunk_size": 10}}