import numpy as np
import matplotlib.pyplot as plt
from scipy import ndimage
from scipy.optimize import least_squares
from skimage import measure, morphology
from skimage.segmentation import watershed
from skimage.feature import peak_local_max
import pandas as pd
import os
import glob
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

class DAZ3DMeasurement:
    """3D曲面柱子自动测量系统"""
    
    def __init__(self, min_pillar_height=1.0, min_pillar_area=100, max_noise_area=50):
        """
        初始化测量参数
        
        Args:
            min_pillar_height: 柱子最小高度阈值(微米)
            min_pillar_area: 柱子最小面积阈值(像素)
            max_noise_area: 噪声最大面积阈值(像素)
        """
        self.min_pillar_height = min_pillar_height
        self.min_pillar_area = min_pillar_area
        self.max_noise_area = max_noise_area
        
    def read_daz_file(self, filepath: str) -> Tuple[np.ndarray, float, float]:
        """
        读取DAZ 3D数据文件 - 与read_file.py保持一致的读取方式

        Args:
            filepath: 文件路径

        Returns:
            daz_matrix: 3D高度矩阵，daz_matrix[x1][y1]表示x1*x_step, y1*y_step位置的高度
            x_step: X方向步长(微米)
            y_step: Y方向步长(微米)
        """
        # 按照read_file.py的方式读取数据
        with open(filepath, 'r') as daz_file:
            daz_data = daz_file.read().strip()

        daz_list = daz_data.split(' ')
        daz_header = daz_list[0:4]
        x_len, y_len = map(int, daz_header[:2])
        x_step, y_step = map(float, daz_header[2:])
        daz_body = list(map(float, daz_list[4:]))

        # 构造符合笛卡尔坐标系的矩阵 - 与read_file.py完全一致
        daz_matrix = np.fliplr(np.array(daz_body).reshape(y_len, x_len).T)

        return daz_matrix, x_step, y_step
    
    def calibrate_plane(self, height_matrix: np.ndarray, mask: np.ndarray = None) -> np.ndarray:
        """
        平面校准：消除基底的翘曲和倾斜
        
        Args:
            height_matrix: 原始高度矩阵
            mask: 掩码，True表示用于平面拟合的区域
            
        Returns:
            calibrated_matrix: 校准后的高度矩阵
        """
        rows, cols = height_matrix.shape
        
        # 如果没有提供掩码，使用全部区域
        if mask is None:
            mask = np.ones_like(height_matrix, dtype=bool)
        
        # 创建坐标网格
        x, y = np.meshgrid(np.arange(cols), np.arange(rows))
        
        # 提取用于拟合的点
        x_fit = x[mask].flatten()
        y_fit = y[mask].flatten()
        z_fit = height_matrix[mask].flatten()
        
        # 平面拟合: z = ax + by + c
        def plane_residuals(params):
            a, b, c = params
            return z_fit - (a * x_fit + b * y_fit + c)
        
        # 初始猜测
        initial_params = [0, 0, np.mean(z_fit)]
        result = least_squares(plane_residuals, initial_params)
        a, b, c = result.x
        
        # 计算拟合平面
        fitted_plane = a * x + b * y + c
        
        # 校准：原始数据减去拟合平面
        calibrated_matrix = height_matrix - fitted_plane
        
        return calibrated_matrix
    
    def detect_pillars(self, calibrated_matrix: np.ndarray, x_step: float, y_step: float) -> List[Dict]:
        """
        检测柱子位置和基本信息
        
        Args:
            calibrated_matrix: 校准后的高度矩阵
            x_step: X方向步长
            y_step: Y方向步长
            
        Returns:
            pillars: 柱子信息列表
        """
        # 高度阈值分割
        binary_mask = calibrated_matrix > self.min_pillar_height
        
        # 形态学处理：去除小噪声
        binary_mask = morphology.remove_small_objects(binary_mask, min_size=self.max_noise_area)
        binary_mask = morphology.remove_small_holes(binary_mask, area_threshold=self.max_noise_area)
        
        # 连通域标记
        labeled_mask = measure.label(binary_mask)
        regions = measure.regionprops(labeled_mask, intensity_image=calibrated_matrix)
        
        pillars = []
        for region in regions:
            # 过滤太小的区域
            if region.area < self.min_pillar_area:
                continue
                
            # 提取柱子信息
            pillar_info = {
                'label': region.label,
                'centroid': region.centroid,
                'area': region.area,
                'bbox': region.bbox,  # (min_row, min_col, max_row, max_col)
                'max_height': region.max_intensity,
                'mean_height': region.mean_intensity,
                'coords': region.coords,
                'x_step': x_step,
                'y_step': y_step
            }
            pillars.append(pillar_info)
        
        return pillars
    
    def measure_pillar_dimensions(self, pillar_info: Dict, calibrated_matrix: np.ndarray) -> Dict:
        """
        测量单个柱子的详细尺寸
        
        Args:
            pillar_info: 柱子基本信息
            calibrated_matrix: 校准后的高度矩阵
            
        Returns:
            measurements: 详细测量结果
        """
        coords = pillar_info['coords']
        max_height = pillar_info['max_height']
        x_step = pillar_info['x_step']
        y_step = pillar_info['y_step']
        
        # 提取柱子区域的高度数据
        pillar_heights = calibrated_matrix[coords[:, 0], coords[:, 1]]
        
        # 计算95%和10%高度位置
        height_95 = max_height * 0.95
        height_10 = max_height * 0.10
        
        measurements = {
            'height': max_height,
            'centroid_x': pillar_info['centroid'][1] * x_step,
            'centroid_y': pillar_info['centroid'][0] * y_step,
            'area_pixels': pillar_info['area'],
            'area_um2': pillar_info['area'] * x_step * y_step
        }
        
        # 在95%和10%高度位置测量宽度
        for height_ratio, height_threshold in [('95%', height_95), ('10%', height_10)]:
            # 找到该高度以上的点
            mask_height = pillar_heights >= height_threshold
            if np.sum(mask_height) == 0:
                measurements[f'width_x_{height_ratio}'] = 0
                measurements[f'width_y_{height_ratio}'] = 0
                continue
            
            # 获取该高度的坐标
            height_coords = coords[mask_height]
            
            # 计算X和Y方向的宽度
            x_coords = height_coords[:, 1]
            y_coords = height_coords[:, 0]
            
            width_x = (np.max(x_coords) - np.min(x_coords) + 1) * x_step
            width_y = (np.max(y_coords) - np.min(y_coords) + 1) * y_step
            
            measurements[f'width_x_{height_ratio}'] = width_x
            measurements[f'width_y_{height_ratio}'] = width_y
        
        return measurements
    
    def process_single_file(self, filepath: str) -> List[Dict]:
        """
        处理单个3D文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            results: 测量结果列表
        """
        print(f"处理文件: {filepath}")
        
        # 读取数据
        height_matrix, x_step, y_step = self.read_daz_file(filepath)
        
        # 平面校准（初步校准，后续可以改进为排除柱子区域）
        calibrated_matrix = self.calibrate_plane(height_matrix)
        
        # 检测柱子
        pillars = self.detect_pillars(calibrated_matrix, x_step, y_step)
        
        # 测量每个柱子
        results = []
        for i, pillar in enumerate(pillars):
            measurements = self.measure_pillar_dimensions(pillar, calibrated_matrix)
            measurements['file'] = os.path.basename(filepath)
            measurements['pillar_id'] = i + 1
            results.append(measurements)
        
        print(f"检测到 {len(results)} 个柱子")
        return results
    
    def batch_process(self, input_dir: str, pattern: str = "*.txt") -> pd.DataFrame:
        """
        批量处理多个文件
        
        Args:
            input_dir: 输入目录
            pattern: 文件匹配模式
            
        Returns:
            df_results: 汇总结果DataFrame
        """
        file_list = glob.glob(os.path.join(input_dir, pattern))
        
        if not file_list:
            print(f"在目录 {input_dir} 中未找到匹配 {pattern} 的文件")
            return pd.DataFrame()
        
        all_results = []
        for filepath in file_list:
            try:
                results = self.process_single_file(filepath)
                all_results.extend(results)
            except Exception as e:
                print(f"处理文件 {filepath} 时出错: {e}")
                continue
        
        if all_results:
            df_results = pd.DataFrame(all_results)
            return df_results
        else:
            return pd.DataFrame()
    
    def save_results(self, df_results: pd.DataFrame, output_path: str):
        """
        保存测量结果
        
        Args:
            df_results: 结果DataFrame
            output_path: 输出文件路径
        """
        df_results.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {output_path}")
    
    def visualize_results(self, filepath: str, save_plot: bool = True):
        """
        可视化单个文件的处理结果
        
        Args:
            filepath: 文件路径
            save_plot: 是否保存图像
        """
        # 读取和处理数据
        height_matrix, x_step, y_step = self.read_daz_file(filepath)
        calibrated_matrix = self.calibrate_plane(height_matrix)
        pillars = self.detect_pillars(calibrated_matrix, x_step, y_step)
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 原始数据
        im1 = axes[0, 0].imshow(height_matrix, cmap='viridis')
        axes[0, 0].set_title('原始高度数据')
        plt.colorbar(im1, ax=axes[0, 0])
        
        # 校准后数据
        im2 = axes[0, 1].imshow(calibrated_matrix, cmap='viridis')
        axes[0, 1].set_title('平面校准后')
        plt.colorbar(im2, ax=axes[0, 1])
        
        # 柱子检测结果
        binary_mask = calibrated_matrix > self.min_pillar_height
        axes[1, 0].imshow(binary_mask, cmap='gray')
        axes[1, 0].set_title('柱子检测结果')
        
        # 标记柱子中心
        axes[1, 1].imshow(calibrated_matrix, cmap='viridis')
        for i, pillar in enumerate(pillars):
            y, x = pillar['centroid']
            axes[1, 1].plot(x, y, 'ro', markersize=8)
            axes[1, 1].text(x, y, str(i+1), color='white', fontsize=12, ha='center')
        axes[1, 1].set_title('柱子标记')
        
        plt.tight_layout()
        
        if save_plot:
            # 确保使用正确的文件扩展名
            plot_path = filepath.replace('.txt', '_analysis.png').replace('.TXT', '_analysis.png')
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"分析图像已保存到: {plot_path}")

        plt.show()


# 使用示例
if __name__ == "__main__":
    # 创建测量实例
    measurer = DAZ3DMeasurement(
        min_pillar_height=1.0,  # 最小柱子高度1微米
        min_pillar_area=100,    # 最小柱子面积100像素
        max_noise_area=50       # 最大噪声面积50像素
    )
    
    # 处理单个文件并可视化
    single_file = "N001.TXT"
    if os.path.exists(single_file):
        measurer.visualize_results(single_file)
        results = measurer.process_single_file(single_file)
        
        # 显示结果
        if results:
            df_single = pd.DataFrame(results)
            print("\n单文件测量结果:")
            print(df_single.to_string(index=False))
    
    # 批量处理（如果有多个文件）
    current_dir = "."
    df_all = measurer.batch_process(current_dir, "*.TXT")
    
    if not df_all.empty:
        # 保存结果
        measurer.save_results(df_all, "measurement_results.csv")
        
        # 显示统计信息
        print(f"\n批量处理完成，共处理 {len(df_all)} 个柱子")
        print(f"平均柱子高度: {df_all['height'].mean():.2f} 微米")
        print(f"平均95%高度X宽度: {df_all['width_x_95%'].mean():.2f} 微米")
        print(f"平均95%高度Y宽度: {df_all['width_y_95%'].mean():.2f} 微米")
