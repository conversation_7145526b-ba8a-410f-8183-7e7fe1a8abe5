import numpy as np
from scipy.optimize import least_squares
from scipy import ndimage
from skimage import morphology, filters
import matplotlib.pyplot as plt

class AdvancedPlaneCalibration:
    """高级平面校准模块：智能排除柱子区域进行基底平面拟合"""
    
    def __init__(self, initial_height_threshold=2.0, iteration_threshold=0.5):
        """
        初始化校准参数
        
        Args:
            initial_height_threshold: 初始高度阈值，用于粗略识别柱子
            iteration_threshold: 迭代阈值，用于精细化校准
        """
        self.initial_height_threshold = initial_height_threshold
        self.iteration_threshold = iteration_threshold
    
    def robust_plane_fit(self, height_matrix: np.ndarray, max_iterations=3) -> np.ndarray:
        """
        鲁棒的平面拟合：迭代排除柱子区域
        
        Args:
            height_matrix: 原始高度矩阵
            max_iterations: 最大迭代次数
            
        Returns:
            calibrated_matrix: 校准后的高度矩阵
        """
        rows, cols = height_matrix.shape
        x, y = np.meshgrid(np.arange(cols), np.arange(rows))
        
        current_matrix = height_matrix.copy()
        
        for iteration in range(max_iterations):
            print(f"平面校准迭代 {iteration + 1}/{max_iterations}")
            
            # 识别可能的柱子区域
            if iteration == 0:
                # 第一次迭代：使用固定阈值
                threshold = self.initial_height_threshold
            else:
                # 后续迭代：使用动态阈值
                threshold = np.std(current_matrix) * 2 + np.mean(current_matrix)
            
            # 创建基底掩码（排除柱子区域）
            substrate_mask = self._create_substrate_mask(current_matrix, threshold)
            
            # 使用基底区域拟合平面
            plane_params = self._fit_plane_to_mask(current_matrix, substrate_mask, x, y)
            
            # 计算拟合平面
            fitted_plane = plane_params[0] * x + plane_params[1] * y + plane_params[2]
            
            # 更新校准矩阵
            new_matrix = current_matrix - fitted_plane
            
            # 检查收敛性
            if iteration > 0:
                change = np.std(new_matrix - current_matrix)
                print(f"  变化量: {change:.6f}")
                if change < self.iteration_threshold:
                    print("  已收敛")
                    break
            
            current_matrix = new_matrix
        
        return current_matrix
    
    def _create_substrate_mask(self, height_matrix: np.ndarray, threshold: float) -> np.ndarray:
        """
        创建基底掩码，排除柱子和噪声区域
        
        Args:
            height_matrix: 高度矩阵
            threshold: 高度阈值
            
        Returns:
            substrate_mask: 基底掩码（True表示基底区域）
        """
        # 基于高度阈值的初步分割
        pillar_mask = height_matrix > threshold
        
        # 形态学处理：去除小噪声，保留主要柱子
        pillar_mask = morphology.remove_small_objects(pillar_mask, min_size=50)
        pillar_mask = morphology.binary_dilation(pillar_mask, morphology.disk(3))  # 扩展柱子区域
        
        # 基底掩码是柱子掩码的反向
        substrate_mask = ~pillar_mask
        
        # 进一步去除边缘区域（可能不稳定）
        border_width = 5
        substrate_mask[:border_width, :] = False
        substrate_mask[-border_width:, :] = False
        substrate_mask[:, :border_width] = False
        substrate_mask[:, -border_width:] = False
        
        return substrate_mask
    
    def _fit_plane_to_mask(self, height_matrix: np.ndarray, mask: np.ndarray, 
                          x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """
        对掩码区域拟合平面
        
        Args:
            height_matrix: 高度矩阵
            mask: 拟合区域掩码
            x, y: 坐标网格
            
        Returns:
            plane_params: 平面参数 [a, b, c] for z = ax + by + c
        """
        # 提取拟合点
        x_fit = x[mask].flatten()
        y_fit = y[mask].flatten()
        z_fit = height_matrix[mask].flatten()
        
        if len(x_fit) < 10:  # 确保有足够的点进行拟合
            print("警告：拟合点数量不足，使用全局拟合")
            x_fit = x.flatten()
            y_fit = y.flatten()
            z_fit = height_matrix.flatten()
        
        # 鲁棒平面拟合
        def plane_residuals(params):
            a, b, c = params
            return z_fit - (a * x_fit + b * y_fit + c)
        
        # 初始猜测
        initial_params = [0, 0, np.median(z_fit)]
        
        # 使用鲁棒优化
        result = least_squares(plane_residuals, initial_params, loss='huber')
        
        return result.x
    
    def adaptive_threshold_calibration(self, height_matrix: np.ndarray) -> np.ndarray:
        """
        自适应阈值校准：根据数据特征自动确定参数
        
        Args:
            height_matrix: 原始高度矩阵
            
        Returns:
            calibrated_matrix: 校准后的高度矩阵
        """
        # 分析数据特征
        data_std = np.std(height_matrix)
        data_range = np.ptp(height_matrix)  # peak-to-peak
        
        print(f"数据分析:")
        print(f"  标准差: {data_std:.3f}")
        print(f"  数据范围: {data_range:.3f}")
        
        # 自适应设置阈值
        if data_range > 10:  # 大范围数据
            self.initial_height_threshold = data_std * 3
        elif data_range > 5:  # 中等范围数据
            self.initial_height_threshold = data_std * 2
        else:  # 小范围数据
            self.initial_height_threshold = data_std * 1.5
        
        print(f"  自适应阈值: {self.initial_height_threshold:.3f}")
        
        return self.robust_plane_fit(height_matrix)
    
    def visualize_calibration_process(self, height_matrix: np.ndarray, 
                                    calibrated_matrix: np.ndarray, 
                                    save_path: str = None):
        """
        可视化校准过程
        
        Args:
            height_matrix: 原始高度矩阵
            calibrated_matrix: 校准后的高度矩阵
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 原始数据
        im1 = axes[0, 0].imshow(height_matrix, cmap='viridis')
        axes[0, 0].set_title('原始高度数据')
        plt.colorbar(im1, ax=axes[0, 0])
        
        # 校准后数据
        im2 = axes[0, 1].imshow(calibrated_matrix, cmap='viridis')
        axes[0, 1].set_title('校准后高度数据')
        plt.colorbar(im2, ax=axes[0, 1])
        
        # 校准差异
        diff = height_matrix - calibrated_matrix
        im3 = axes[0, 2].imshow(diff, cmap='RdBu_r')
        axes[0, 2].set_title('校准差异（拟合平面）')
        plt.colorbar(im3, ax=axes[0, 2])
        
        # 基底掩码
        threshold = np.std(calibrated_matrix) * 2 + np.mean(calibrated_matrix)
        substrate_mask = self._create_substrate_mask(calibrated_matrix, threshold)
        axes[1, 0].imshow(substrate_mask, cmap='gray')
        axes[1, 0].set_title('基底区域掩码')
        
        # 高度分布直方图
        axes[1, 1].hist(height_matrix.flatten(), bins=50, alpha=0.7, label='原始', density=True)
        axes[1, 1].hist(calibrated_matrix.flatten(), bins=50, alpha=0.7, label='校准后', density=True)
        axes[1, 1].set_xlabel('高度 (μm)')
        axes[1, 1].set_ylabel('密度')
        axes[1, 1].legend()
        axes[1, 1].set_title('高度分布对比')
        
        # 3D视图（校准后）
        from mpl_toolkits.mplot3d import Axes3D
        ax_3d = fig.add_subplot(2, 3, 6, projection='3d')
        
        # 降采样以提高显示性能
        step = max(1, min(height_matrix.shape) // 50)
        x_3d = np.arange(0, height_matrix.shape[1], step)
        y_3d = np.arange(0, height_matrix.shape[0], step)
        X_3d, Y_3d = np.meshgrid(x_3d, y_3d)
        Z_3d = calibrated_matrix[::step, ::step]
        
        ax_3d.plot_surface(X_3d, Y_3d, Z_3d, cmap='viridis', alpha=0.8)
        ax_3d.set_title('校准后3D视图')
        ax_3d.set_xlabel('X')
        ax_3d.set_ylabel('Y')
        ax_3d.set_zlabel('高度 (μm)')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"校准过程图像已保存到: {save_path}")
        
        plt.show()


# 集成到主测量类的方法
def integrate_advanced_calibration():
    """
    展示如何将高级校准集成到主测量系统中
    """
    from auto_measurement import DAZ3DMeasurement
    
    class EnhancedDAZ3DMeasurement(DAZ3DMeasurement):
        """增强版3D测量系统，集成高级平面校准"""
        
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.calibrator = AdvancedPlaneCalibration()
        
        def calibrate_plane(self, height_matrix: np.ndarray, mask: np.ndarray = None) -> np.ndarray:
            """
            重写平面校准方法，使用高级校准算法
            """
            return self.calibrator.adaptive_threshold_calibration(height_matrix)
        
        def process_single_file_enhanced(self, filepath: str, visualize: bool = True) -> List[Dict]:
            """
            增强版单文件处理，包含详细的校准可视化
            """
            print(f"增强处理文件: {filepath}")
            
            # 读取数据
            height_matrix, x_step, y_step = self.read_daz_file(filepath)
            
            # 高级平面校准
            calibrated_matrix = self.calibrate_plane(height_matrix)
            
            # 可视化校准过程
            if visualize:
                plot_path = filepath.replace('.txt', '_calibration.png').replace('.TXT', '_calibration.png')
                self.calibrator.visualize_calibration_process(
                    height_matrix, calibrated_matrix, plot_path
                )
            
            # 检测和测量柱子
            pillars = self.detect_pillars(calibrated_matrix, x_step, y_step)
            
            results = []
            for i, pillar in enumerate(pillars):
                measurements = self.measure_pillar_dimensions(pillar, calibrated_matrix)
                measurements['file'] = os.path.basename(filepath)
                measurements['pillar_id'] = i + 1
                results.append(measurements)
            
            print(f"检测到 {len(results)} 个柱子")
            return results
    
    return EnhancedDAZ3DMeasurement


# 使用示例
if __name__ == "__main__":
    import os
    
    # 创建高级校准器
    calibrator = AdvancedPlaneCalibration()
    
    # 如果有测试文件，演示校准过程
    test_file = "N001.TXT"
    if os.path.exists(test_file):
        # 读取数据（需要导入主模块的读取函数）
        from auto_measurement import DAZ3DMeasurement
        
        measurer = DAZ3DMeasurement()
        height_matrix, x_step, y_step = measurer.read_daz_file(test_file)
        
        print("开始高级平面校准...")
        calibrated_matrix = calibrator.adaptive_threshold_calibration(height_matrix)
        
        # 可视化校准过程
        calibrator.visualize_calibration_process(
            height_matrix, calibrated_matrix, 
            test_file.replace('.TXT', '_advanced_calibration.png')
        )
        
        print("高级校准完成！")
    else:
        print(f"测试文件 {test_file} 不存在")
