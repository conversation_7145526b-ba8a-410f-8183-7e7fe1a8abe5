#!/usr/bin/env python3
"""
3D曲面柱子自动测量 - 简化使用示例

这个脚本展示了如何使用系统进行柱子测量的基本流程
"""

import os
import pandas as pd
from auto_measurement import DAZ3DMeasurement

def measure_single_file(filename):
    """测量单个文件中的柱子"""
    print(f"\n{'='*50}")
    print(f"测量文件: {filename}")
    print(f"{'='*50}")
    
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return None
    
    # 创建测量实例
    measurer = DAZ3DMeasurement(
        min_pillar_height=1.0,    # 柱子最小高度1微米
        min_pillar_area=100,      # 柱子最小面积100像素
        max_noise_area=50         # 噪声最大面积50像素
    )
    
    # 处理文件
    results = measurer.process_single_file(filename)
    
    if results:
        # 转换为DataFrame便于查看
        df = pd.DataFrame(results)
        
        print(f"\n检测到 {len(results)} 个柱子:")
        print("-" * 80)
        
        # 显示详细结果
        for i, result in enumerate(results):
            print(f"柱子 {i+1}:")
            print(f"  位置: ({result['centroid_x']:.1f}, {result['centroid_y']:.1f}) μm")
            print(f"  高度: {result['height']:.2f} μm")
            print(f"  面积: {result['area_um2']:.1f} μm²")
            print(f"  95%高度位置宽度: X={result['width_x_95%']:.2f} μm, Y={result['width_y_95%']:.2f} μm")
            print(f"  10%高度位置宽度: X={result['width_x_10%']:.2f} μm, Y={result['width_y_10%']:.2f} μm")
            
            # 计算长短轴比（椭圆度分析）
            aspect_ratio_95 = max(result['width_x_95%'], result['width_y_95%']) / min(result['width_x_95%'], result['width_y_95%'])
            aspect_ratio_10 = max(result['width_x_10%'], result['width_y_10%']) / min(result['width_x_10%'], result['width_y_10%'])
            
            print(f"  95%高度长短轴比: {aspect_ratio_95:.2f}")
            print(f"  10%高度长短轴比: {aspect_ratio_10:.2f}")
            
            # 形状判断
            if aspect_ratio_95 < 1.2:
                shape_95 = "近似圆形"
            elif aspect_ratio_95 < 2.0:
                shape_95 = "椭圆形"
            else:
                shape_95 = "长椭圆形"
            
            print(f"  95%高度形状: {shape_95}")
            print()
        
        # 保存结果
        output_file = filename.replace('.TXT', '_results.csv').replace('.txt', '_results.csv')
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {output_file}")
        
        # 生成可视化
        measurer.visualize_results(filename, save_plot=True)
        
        return df
    else:
        print("未检测到柱子")
        return None

def batch_measure_directory(directory=".", pattern="*.TXT"):
    """批量测量目录中的所有文件"""
    print(f"\n{'='*50}")
    print(f"批量测量目录: {directory}")
    print(f"文件模式: {pattern}")
    print(f"{'='*50}")
    
    # 创建测量实例
    measurer = DAZ3DMeasurement(
        min_pillar_height=1.0,
        min_pillar_area=100,
        max_noise_area=50
    )
    
    # 批量处理
    df_all = measurer.batch_process(directory, pattern)
    
    if not df_all.empty:
        print(f"\n批量处理完成!")
        print(f"总共处理了 {len(df_all)} 个柱子")
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"  文件数量: {df_all['file'].nunique()}")
        print(f"  平均柱子高度: {df_all['height'].mean():.2f} ± {df_all['height'].std():.2f} μm")
        print(f"  平均柱子面积: {df_all['area_um2'].mean():.1f} ± {df_all['area_um2'].std():.1f} μm²")
        print(f"  平均95%高度X宽度: {df_all['width_x_95%'].mean():.2f} ± {df_all['width_x_95%'].std():.2f} μm")
        print(f"  平均95%高度Y宽度: {df_all['width_y_95%'].mean():.2f} ± {df_all['width_y_95%'].std():.2f} μm")
        
        # 保存汇总结果
        summary_file = "batch_measurement_summary.csv"
        measurer.save_results(df_all, summary_file)
        
        return df_all
    else:
        print("未找到有效的测量结果")
        return pd.DataFrame()

def analyze_pillar_shape(df_results):
    """分析柱子形状特征"""
    if df_results is None or df_results.empty:
        return
    
    print(f"\n{'='*50}")
    print("柱子形状分析")
    print(f"{'='*50}")
    
    # 计算长短轴比
    df_results['aspect_ratio_95%'] = df_results[['width_x_95%', 'width_y_95%']].max(axis=1) / df_results[['width_x_95%', 'width_y_95%']].min(axis=1)
    df_results['aspect_ratio_10%'] = df_results[['width_x_10%', 'width_y_10%']].max(axis=1) / df_results[['width_x_10%', 'width_y_10%']].min(axis=1)
    
    # 形状分类
    def classify_shape(aspect_ratio):
        if aspect_ratio < 1.2:
            return "圆形"
        elif aspect_ratio < 2.0:
            return "椭圆形"
        else:
            return "长椭圆形"
    
    df_results['shape_95%'] = df_results['aspect_ratio_95%'].apply(classify_shape)
    df_results['shape_10%'] = df_results['aspect_ratio_10%'].apply(classify_shape)
    
    # 统计形状分布
    print("95%高度位置形状分布:")
    shape_counts_95 = df_results['shape_95%'].value_counts()
    for shape, count in shape_counts_95.items():
        print(f"  {shape}: {count} 个 ({count/len(df_results)*100:.1f}%)")
    
    print("\n10%高度位置形状分布:")
    shape_counts_10 = df_results['shape_10%'].value_counts()
    for shape, count in shape_counts_10.items():
        print(f"  {shape}: {count} 个 ({count/len(df_results)*100:.1f}%)")
    
    # 保存形状分析结果
    shape_analysis_file = "pillar_shape_analysis.csv"
    df_results.to_csv(shape_analysis_file, index=False, encoding='utf-8-sig')
    print(f"\n形状分析结果已保存到: {shape_analysis_file}")
    
    return df_results

def main():
    """主函数"""
    print("3D曲面柱子自动测量系统")
    print("=" * 60)
    
    # 示例1: 测量单个文件
    single_file = "N001.TXT"
    if os.path.exists(single_file):
        df_single = measure_single_file(single_file)
        
        # 分析单个文件的柱子形状
        if df_single is not None:
            analyze_pillar_shape(df_single)
    else:
        print(f"单个测试文件 {single_file} 不存在")
    
    # 示例2: 批量测量
    print(f"\n{'='*60}")
    print("开始批量测量...")
    
    df_batch = batch_measure_directory(".", "*.TXT")
    
    if not df_batch.empty:
        # 分析所有柱子的形状
        analyze_pillar_shape(df_batch)
        
        # 按文件分组显示结果
        print(f"\n{'='*50}")
        print("按文件分组的结果:")
        print(f"{'='*50}")
        
        for filename in df_batch['file'].unique():
            file_data = df_batch[df_batch['file'] == filename]
            print(f"\n文件: {filename}")
            print(f"  柱子数量: {len(file_data)}")
            print(f"  平均高度: {file_data['height'].mean():.2f} μm")
            print(f"  平均面积: {file_data['area_um2'].mean():.1f} μm²")
    
    print(f"\n{'='*60}")
    print("测量完成！")
    print("生成的文件:")
    print("  - measurement_results.csv (原始测量结果)")
    print("  - batch_measurement_summary.csv (批量测量汇总)")
    print("  - pillar_shape_analysis.csv (形状分析结果)")
    print("  - N001_analysis.png (可视化分析图)")
    print("  - N001_advanced_calibration.png (高级校准过程图)")

if __name__ == "__main__":
    main()
