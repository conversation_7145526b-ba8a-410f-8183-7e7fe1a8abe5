#!/usr/bin/env python3
"""
柱子切面分析脚本 - 生成X、Y方向切面图并标注尺寸
"""

import numpy as np
import matplotlib.pyplot as plt
from auto_measurement import DAZ3DMeasurement
import os

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def detailed_cross_section_analysis(filepath):
    """详细的切面分析"""
    print(f"开始详细切面分析: {filepath}")
    
    if not os.path.exists(filepath):
        print(f"文件 {filepath} 不存在")
        return
    
    # 创建测量实例
    measurer = DAZ3DMeasurement(
        min_pillar_height=1.0,
        min_pillar_area=100,
        max_noise_area=50
    )
    
    # 读取和校准数据
    height_matrix, x_step, y_step = measurer.read_daz_file(filepath)
    calibrated_matrix = measurer.calibrate_plane(height_matrix)
    
    # 检测柱子
    pillars = measurer.detect_pillars(calibrated_matrix, x_step, y_step)
    
    if not pillars:
        print("未检测到柱子")
        return
    
    print(f"检测到 {len(pillars)} 个柱子")
    
    # 为每个柱子生成详细的切面分析
    for i, pillar in enumerate(pillars):
        print(f"\n分析柱子 {i+1}...")
        
        # 生成切面图
        measurer.visualize_pillar_cross_sections(pillar, calibrated_matrix, 
                                               filepath.replace('.TXT', f'_pillar_{i+1}'))
        
        # 生成详细的尺寸分析报告
        generate_dimension_report(pillar, calibrated_matrix, x_step, y_step, i+1)

def generate_dimension_report(pillar_info, calibrated_matrix, x_step, y_step, pillar_id):
    """生成详细的尺寸分析报告"""
    coords = pillar_info['coords']
    max_height = pillar_info['max_height']
    centroid = pillar_info['centroid']
    
    print(f"\n柱子 {pillar_id} 详细尺寸报告:")
    print("=" * 50)
    
    # 基本信息
    print(f"柱子中心位置: ({centroid[1]*x_step:.2f}, {centroid[0]*y_step:.2f}) μm")
    print(f"柱子最大高度: {max_height:.3f} μm")
    print(f"柱子面积: {len(coords)} 像素 ({len(coords)*x_step*y_step:.2f} μm²)")
    
    # 多层级高度分析
    height_levels = [0.1, 0.25, 0.5, 0.75, 0.9, 0.95]
    
    print(f"\n多层级宽度分析:")
    print("-" * 30)
    print(f"{'高度比例':<8} {'高度(μm)':<10} {'X宽度(μm)':<12} {'Y宽度(μm)':<12} {'长短轴比':<10}")
    print("-" * 60)
    
    pillar_heights = calibrated_matrix[coords[:, 0], coords[:, 1]]
    
    for level in height_levels:
        height_threshold = max_height * level
        mask_height = pillar_heights >= height_threshold
        
        if np.sum(mask_height) > 0:
            height_coords = coords[mask_height]
            x_coords = height_coords[:, 1]
            y_coords = height_coords[:, 0]
            
            width_x = (np.max(x_coords) - np.min(x_coords) + 1) * x_step
            width_y = (np.max(y_coords) - np.min(y_coords) + 1) * y_step
            
            aspect_ratio = max(width_x, width_y) / min(width_x, width_y)
            
            print(f"{level*100:>6.0f}%   {height_threshold:>8.3f}   {width_x:>10.2f}   {width_y:>10.2f}   {aspect_ratio:>8.2f}")
        else:
            print(f"{level*100:>6.0f}%   {height_threshold:>8.3f}   {'N/A':>10}   {'N/A':>10}   {'N/A':>8}")
    
    # 形状分析
    print(f"\n形状特征分析:")
    print("-" * 20)
    
    # 95%高度形状分析
    height_95 = max_height * 0.95
    mask_95 = pillar_heights >= height_95
    if np.sum(mask_95) > 0:
        coords_95 = coords[mask_95]
        width_x_95 = (np.max(coords_95[:, 1]) - np.min(coords_95[:, 1]) + 1) * x_step
        width_y_95 = (np.max(coords_95[:, 0]) - np.min(coords_95[:, 0]) + 1) * y_step
        aspect_ratio_95 = max(width_x_95, width_y_95) / min(width_x_95, width_y_95)
        
        if aspect_ratio_95 < 1.2:
            shape_type = "近似圆形"
        elif aspect_ratio_95 < 2.0:
            shape_type = "椭圆形"
        else:
            shape_type = "长椭圆形"
        
        print(f"95%高度形状类型: {shape_type}")
        print(f"95%高度长短轴比: {aspect_ratio_95:.2f}")
        
        # 计算等效直径
        area_95 = np.sum(mask_95) * x_step * y_step
        equivalent_diameter = 2 * np.sqrt(area_95 / np.pi)
        print(f"95%高度等效直径: {equivalent_diameter:.2f} μm")

def compare_multiple_height_levels(filepath):
    """比较多个高度层级的宽度变化"""
    print(f"\n多层级宽度变化分析: {filepath}")
    
    if not os.path.exists(filepath):
        print(f"文件 {filepath} 不存在")
        return
    
    # 创建测量实例
    measurer = DAZ3DMeasurement()
    
    # 读取和校准数据
    height_matrix, x_step, y_step = measurer.read_daz_file(filepath)
    calibrated_matrix = measurer.calibrate_plane(height_matrix)
    pillars = measurer.detect_pillars(calibrated_matrix, x_step, y_step)
    
    if not pillars:
        print("未检测到柱子")
        return
    
    # 分析第一个柱子的多层级宽度变化
    pillar = pillars[0]
    coords = pillar['coords']
    max_height = pillar['max_height']
    pillar_heights = calibrated_matrix[coords[:, 0], coords[:, 1]]
    
    # 定义更多的高度层级
    height_levels = np.arange(0.1, 1.0, 0.05)  # 从10%到95%，每5%一个层级
    
    widths_x = []
    widths_y = []
    heights = []
    
    for level in height_levels:
        height_threshold = max_height * level
        mask_height = pillar_heights >= height_threshold
        
        if np.sum(mask_height) > 10:  # 至少10个点
            height_coords = coords[mask_height]
            x_coords = height_coords[:, 1]
            y_coords = height_coords[:, 0]
            
            width_x = (np.max(x_coords) - np.min(x_coords) + 1) * x_step
            width_y = (np.max(y_coords) - np.min(y_coords) + 1) * y_step
            
            widths_x.append(width_x)
            widths_y.append(width_y)
            heights.append(level * 100)
    
    # 绘制宽度变化曲线
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # X方向宽度变化
    ax1.plot(heights, widths_x, 'b-o', linewidth=2, markersize=6, label='X方向宽度')
    ax1.axhline(y=widths_x[heights.index(95.0)] if 95.0 in heights else widths_x[-1], 
                color='r', linestyle='--', alpha=0.7, label='95%高度宽度')
    ax1.axhline(y=widths_x[heights.index(10.0)] if 10.0 in heights else widths_x[0], 
                color='g', linestyle='--', alpha=0.7, label='10%高度宽度')
    ax1.set_xlabel('高度百分比 (%)')
    ax1.set_ylabel('X方向宽度 (μm)')
    ax1.set_title('X方向宽度随高度变化')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Y方向宽度变化
    ax2.plot(heights, widths_y, 'r-o', linewidth=2, markersize=6, label='Y方向宽度')
    ax2.axhline(y=widths_y[heights.index(95.0)] if 95.0 in heights else widths_y[-1], 
                color='r', linestyle='--', alpha=0.7, label='95%高度宽度')
    ax2.axhline(y=widths_y[heights.index(10.0)] if 10.0 in heights else widths_y[0], 
                color='g', linestyle='--', alpha=0.7, label='10%高度宽度')
    ax2.set_xlabel('高度百分比 (%)')
    ax2.set_ylabel('Y方向宽度 (μm)')
    ax2.set_title('Y方向宽度随高度变化')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    plot_path = filepath.replace('.TXT', '_width_variation.png').replace('.txt', '_width_variation.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"宽度变化分析图已保存到: {plot_path}")
    
    plt.show()

def main():
    """主函数"""
    print("柱子切面分析系统")
    print("=" * 50)
    
    test_file = "N001.TXT"
    
    if os.path.exists(test_file):
        # 1. 详细切面分析
        detailed_cross_section_analysis(test_file)
        
        # 2. 多层级宽度变化分析
        compare_multiple_height_levels(test_file)
        
        print(f"\n分析完成！生成的文件:")
        print("- *_cross_sections.png: 柱子切面分析图")
        print("- *_width_variation.png: 宽度变化分析图")
        
    else:
        print(f"测试文件 {test_file} 不存在")

if __name__ == "__main__":
    main()
