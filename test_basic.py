#!/usr/bin/env python3
"""
基本功能测试脚本
"""

import os
import numpy as np

def test_data_reading():
    """测试数据读取功能"""
    print("测试数据读取功能...")
    
    # 按照read_file.py的方式读取数据
    def read_daz_file_simple(filepath):
        with open(filepath, 'r') as daz_file:
            daz_data = daz_file.read().strip()

        daz_list = daz_data.split(' ')
        daz_header = daz_list[0:4]
        x_len, y_len = map(int, daz_header[:2])
        x_step, y_step = map(float, daz_header[2:])
        daz_body = list(map(float, daz_list[4:]))

        # 构造符合笛卡尔坐标系的矩阵 - 与read_file.py完全一致
        daz_matrix = np.fliplr(np.array(daz_body).reshape(y_len, x_len).T)

        return daz_matrix, x_step, y_step
    
    # 测试文件读取
    test_file = "N001.TXT"
    if os.path.exists(test_file):
        try:
            height_matrix, x_step, y_step = read_daz_file_simple(test_file)
            print(f"✓ 文件读取成功")
            print(f"  数据尺寸: {height_matrix.shape}")
            print(f"  X步长: {x_step:.6f} μm")
            print(f"  Y步长: {y_step:.6f} μm")
            print(f"  高度范围: {np.min(height_matrix):.3f} - {np.max(height_matrix):.3f} μm")
            return height_matrix, x_step, y_step
        except Exception as e:
            print(f"✗ 文件读取失败: {e}")
            return None, None, None
    else:
        print(f"✗ 测试文件 {test_file} 不存在")
        return None, None, None

def test_basic_analysis(height_matrix):
    """测试基本数据分析"""
    if height_matrix is None:
        return
    
    print("\n测试基本数据分析...")
    
    try:
        # 基本统计
        mean_height = np.mean(height_matrix)
        std_height = np.std(height_matrix)
        min_height = np.min(height_matrix)
        max_height = np.max(height_matrix)
        
        print(f"✓ 基本统计计算成功")
        print(f"  平均高度: {mean_height:.3f} μm")
        print(f"  标准差: {std_height:.3f} μm")
        print(f"  最小高度: {min_height:.3f} μm")
        print(f"  最大高度: {max_height:.3f} μm")
        
        # 简单的平面拟合测试
        rows, cols = height_matrix.shape
        x, y = np.meshgrid(np.arange(cols), np.arange(rows))
        
        # 使用最小二乘法拟合平面
        A = np.column_stack([x.flatten(), y.flatten(), np.ones(x.size)])
        b = height_matrix.flatten()
        
        # 求解 Ax = b
        plane_params, residuals, rank, s = np.linalg.lstsq(A, b, rcond=None)
        
        # 计算拟合平面
        fitted_plane = (plane_params[0] * x + 
                       plane_params[1] * y + 
                       plane_params[2])
        
        # 校准后的数据
        calibrated_matrix = height_matrix - fitted_plane
        
        print(f"✓ 平面校准成功")
        print(f"  校准前标准差: {std_height:.3f} μm")
        print(f"  校准后标准差: {np.std(calibrated_matrix):.3f} μm")
        
        return calibrated_matrix
        
    except Exception as e:
        print(f"✗ 基本分析失败: {e}")
        return None

def test_pillar_detection(calibrated_matrix, x_step, y_step):
    """测试柱子检测"""
    if calibrated_matrix is None:
        return
    
    print("\n测试柱子检测...")
    
    try:
        # 简单的阈值检测
        threshold = 1.0  # 1微米阈值
        binary_mask = calibrated_matrix > threshold
        
        # 计算连通区域（简化版）
        from scipy import ndimage
        labeled_array, num_features = ndimage.label(binary_mask)
        
        print(f"✓ 柱子检测成功")
        print(f"  检测阈值: {threshold} μm")
        print(f"  检测到的区域数: {num_features}")
        
        # 分析每个区域
        pillars = []
        for i in range(1, num_features + 1):
            region_mask = labeled_array == i
            region_area = np.sum(region_mask)
            
            if region_area > 50:  # 最小面积过滤
                # 计算区域属性
                coords = np.where(region_mask)
                centroid_y = np.mean(coords[0])
                centroid_x = np.mean(coords[1])
                max_height = np.max(calibrated_matrix[region_mask])
                
                pillar_info = {
                    'id': i,
                    'area_pixels': region_area,
                    'area_um2': region_area * x_step * y_step,
                    'centroid_x': centroid_x * x_step,
                    'centroid_y': centroid_y * y_step,
                    'max_height': max_height
                }
                pillars.append(pillar_info)
        
        print(f"  有效柱子数量: {len(pillars)}")
        
        if pillars:
            print("  柱子信息:")
            for pillar in pillars:
                print(f"    柱子{pillar['id']}: 高度={pillar['max_height']:.2f}μm, "
                      f"面积={pillar['area_um2']:.1f}μm², "
                      f"中心=({pillar['centroid_x']:.1f}, {pillar['centroid_y']:.1f})μm")
        
        return pillars
        
    except Exception as e:
        print(f"✗ 柱子检测失败: {e}")
        return None

def main():
    """主测试函数"""
    print("3D曲面柱子测量系统 - 基本功能测试")
    print("=" * 50)
    
    # 测试1: 数据读取
    height_matrix, x_step, y_step = test_data_reading()
    
    # 测试2: 基本分析
    calibrated_matrix = test_basic_analysis(height_matrix)
    
    # 测试3: 柱子检测
    pillars = test_pillar_detection(calibrated_matrix, x_step, y_step)
    
    print("\n" + "=" * 50)
    if height_matrix is not None and calibrated_matrix is not None and pillars is not None:
        print("✓ 所有基本功能测试通过！")
        print("系统可以正常工作，可以运行完整的测量程序。")
    else:
        print("✗ 部分测试失败")
        print("请检查依赖包安装和数据文件。")

if __name__ == "__main__":
    main()
